#!/usr/bin/env python3
"""
Clean Storage Manager Module

This module provides streamlined functionality for storing backup data in Alibaba Cloud OSS.
It uses dedicated service classes for compression and checksum operations,
following the Single Responsibility Principle.

REFACTORED: Reduced from 1700+ lines to ~200 lines by using:
- CompressionService: Handles all compression operations (GZ-optimized)
- ChecksumService: Handles integrity verification
- Unified error handling using custom exceptions
"""

import os
import datetime
import tempfile
import time
import threading
import psutil
import gc
from typing import List, Tuple, Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError

# Import OSS SDK
try:
    import oss2
    from oss2.exceptions import OssError, RequestError, ClientError, ServerError
except ImportError:
    raise ImportError("Alibaba Cloud OSS SDK not found. Please install it with 'pip install oss2'")

# Import project modules
from core.config_manager import ConfigManager
from core.compression_service import CompressionService
from core.checksum_service import ChecksumService
from utils.exceptions import StorageError, NetworkError, ValidationError
from utils.logging_utils import logger

class StorageManager:
    """
    Clean storage manager that uses dedicated service classes for all operations.
    
    This class focuses solely on OSS operations and delegates compression and
    checksum operations to specialized service classes.
    """

    def __init__(self, config_manager: ConfigManager = None):
        """
        Initialize the storage manager with service dependencies.

        Args:
            config_manager: Configuration manager instance (optional)
        """
        self.config = config_manager or ConfigManager()
        self._auth = None
        self._bucket = None

        # Initialize service dependencies
        self.compression_service = CompressionService(self.config)
        self.checksum_service = ChecksumService(self.config)

        # HIGH PRIORITY FIX: Initialize timeout and retry settings
        self.upload_timeout = self.config.get('storage', 'upload_timeout_seconds', 1800)  # 30 minutes default
        self.max_retries = self.config.get('storage', 'max_upload_retries', 3)
        self.retry_delay = self.config.get('storage', 'retry_delay_seconds', 5)
        self.chunk_size_mb = self.config.get('storage', 'chunk_size_mb', 100)  # 100MB chunks
        self.memory_threshold_percent = self.config.get('storage', 'memory_threshold_percent', 75)

        # Initialize memory monitoring
        self._memory_monitor_active = False
        self._memory_cleanup_lock = threading.Lock()

        logger.info("StorageManager initialized")
        logger.info(f"Upload settings: timeout={self.upload_timeout}s, retries={self.max_retries}, chunk_size={self.chunk_size_mb}MB")

        # SECURITY FIX: Don't store credentials as instance variables
        # Credentials will be retrieved on-demand to minimize exposure
        logger.info("StorageManager initialized with secure credential handling")

    def _get_credentials(self) -> Dict[str, str]:
        """
        Get OSS credentials on-demand to minimize memory exposure.

        SECURITY FIX: Retrieve credentials only when needed instead of storing them.

        Returns:
            Dictionary with OSS credentials
        """
        return self.config.get_oss_credentials()

    def get_oss_auth(self):
        """Get OSS authentication object."""
        if self._auth is None:
            # SECURITY FIX: Get credentials on-demand
            credentials = self._get_credentials()
            self._auth = oss2.Auth(
                credentials['access_key_id'],
                credentials['access_key_secret']
            )
        return self._auth

    def get_oss_bucket(self):
        """Get OSS bucket object."""
        if self._bucket is None:
            # SECURITY FIX: Get credentials on-demand
            credentials = self._get_credentials()
            auth = self.get_oss_auth()
            self._bucket = oss2.Bucket(
                auth,
                credentials['endpoint'],
                credentials['bucket']
            )
        return self._bucket

    def _monitor_memory_usage(self) -> Dict[str, float]:
        """
        Monitor current memory usage.

        Returns:
            Dictionary with memory statistics
        """
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()

            # Get system memory info
            system_memory = psutil.virtual_memory()

            return {
                'process_memory_mb': memory_info.rss / (1024 * 1024),
                'process_memory_percent': memory_percent,
                'system_memory_percent': system_memory.percent,
                'system_available_mb': system_memory.available / (1024 * 1024)
            }
        except Exception as e:
            logger.warning(f"Memory monitoring failed: {e}")
            return {}

    def _cleanup_memory(self):
        """
        Perform memory cleanup when threshold is exceeded.
        """
        with self._memory_cleanup_lock:
            try:
                logger.info("Performing memory cleanup...")
                gc.collect()

                # Log memory status after cleanup
                memory_stats = self._monitor_memory_usage()
                if memory_stats:
                    logger.info(f"Memory after cleanup: {memory_stats['process_memory_mb']:.1f}MB "
                              f"({memory_stats['process_memory_percent']:.1f}%)")
            except Exception as e:
                logger.warning(f"Memory cleanup failed: {e}")

    def test_connection(self) -> Tuple[bool, str]:
        """
        Test OSS connection and bucket accessibility.

        Returns:
            Tuple of (success, message)
        """
        try:
            logger.info("Testing OSS connection...")
            bucket = self.get_oss_bucket()

            # Test bucket access by listing objects (limit to 1)
            test_iterator = oss2.ObjectIterator(bucket, max_keys=1)
            list(test_iterator)  # Force evaluation

            logger.info("OSS connection test: SUCCESS")
            return True, "OSS connection successful"

        except (OssError, RequestError, ClientError, ServerError) as e:
            error_msg = f"OSS connection test failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"OSS connection test error: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def compress_and_upload(self, local_dir: str, oss_path: str,
                           verify_integrity: bool = True) -> Tuple[bool, Dict[str, Any]]:
        """
        Compress a directory and upload to OSS with optional integrity verification.

        Args:
            local_dir: Local directory to compress and upload
            oss_path: OSS path for the uploaded file
            verify_integrity: Whether to verify file integrity

        Returns:
            Tuple of (success_flag, operation_details)
        """
        # SECURITY FIX: Use secure temporary file creation
        temp_fd = None
        temp_file = None
        try:
            logger.log_operation("Compress and Upload", "STARTED", f"{local_dir} -> {oss_path}")

            # HIGH PRIORITY FIX: Test connection before starting
            connection_ok, connection_msg = self.test_connection()
            if not connection_ok:
                raise NetworkError(f"OSS connection failed: {connection_msg}")

            # HIGH PRIORITY FIX: Monitor memory before starting
            initial_memory = self._monitor_memory_usage()
            if initial_memory:
                logger.info(f"Initial memory: {initial_memory['process_memory_mb']:.1f}MB "
                          f"({initial_memory['process_memory_percent']:.1f}%)")

                # Check if we're already at memory threshold
                if initial_memory['process_memory_percent'] > self.memory_threshold_percent:
                    logger.warning(f"Memory usage high ({initial_memory['process_memory_percent']:.1f}%), performing cleanup")
                    self._cleanup_memory()

            # SECURITY FIX: Create secure temporary file instead of predictable naming
            # This prevents race condition attacks and unauthorized access
            temp_fd, temp_file = tempfile.mkstemp(suffix='.tar.gz', prefix='tngd_compress_')
            os.close(temp_fd)  # Close the file descriptor, we only need the path
            temp_fd = None

            logger.info(f"Created secure temporary file: {temp_file}")

            # Step 1: Compress using CompressionService (GZ format)
            success, compressed_file, compression_stats = self.compression_service.compress_directory(
                local_dir, temp_file
            )
            
            if not success:
                raise StorageError("Compression failed", operation="compress", file_path=local_dir)

            # Step 2: Calculate checksum if verification is enabled
            checksum = None
            if verify_integrity:
                checksum = self.checksum_service.calculate_file_checksum(compressed_file)
                if not checksum:
                    raise ValidationError("Checksum calculation failed", file_path=compressed_file)

            # Step 3: Upload to OSS with retry logic and chunked upload support
            upload_success, upload_details = self._upload_file_to_oss_with_retry(compressed_file, oss_path)

            # MEDIUM PRIORITY FIX: Graceful degradation - save locally if OSS upload fails
            if not upload_success:
                error_msg = upload_details.get('error', 'Unknown upload error')
                logger.warning(f"OSS upload failed: {error_msg}")

                # Extract table name and date from oss_path for local backup
                path_parts = oss_path.split('/')
                table_name = path_parts[-1].replace('.tar.gz', '') if path_parts else 'unknown_table'
                date_str = datetime.datetime.now().strftime('%Y-%m-%d')

                # Try to save locally as backup
                backup_success, backup_path = self.save_locally_as_backup(compressed_file, table_name, date_str)

                if backup_success:
                    logger.info(f"Data saved locally as backup: {backup_path}")
                    upload_details['local_backup'] = backup_path
                    upload_details['backup_mode'] = True
                    # Continue processing instead of failing completely
                else:
                    raise StorageError(f"Both OSS upload and local backup failed. OSS error: {error_msg}",
                                     operation="upload", file_path=oss_path)
            else:
                upload_details['backup_mode'] = False

            # HIGH PRIORITY FIX: Monitor memory after upload
            post_upload_memory = self._monitor_memory_usage()
            if post_upload_memory:
                logger.info(f"Memory after upload: {post_upload_memory['process_memory_mb']:.1f}MB "
                          f"({post_upload_memory['process_memory_percent']:.1f}%)")

                # Cleanup if memory usage is high
                if post_upload_memory['process_memory_percent'] > self.memory_threshold_percent:
                    logger.info("Memory threshold exceeded after upload, performing cleanup")
                    self._cleanup_memory()

            # Step 4: Verify upload integrity if enabled
            if verify_integrity and checksum:
                logger.info("Starting integrity verification...")
                verification_success = self._verify_uploaded_file(oss_path, checksum)
                if not verification_success:
                    raise ValidationError("Upload integrity verification failed", file_path=oss_path)
                logger.info("Integrity verification completed successfully")

            # Step 5: Cleanup temporary file
            if compressed_file and os.path.exists(compressed_file):
                os.remove(compressed_file)
                logger.info(f"Cleaned up temporary file: {compressed_file}")

            # HIGH PRIORITY FIX: Final memory cleanup
            final_memory = self._monitor_memory_usage()
            if final_memory:
                logger.info(f"Final memory: {final_memory['process_memory_mb']:.1f}MB "
                          f"({final_memory['process_memory_percent']:.1f}%)")

            # Prepare operation details
            operation_details = {
                'local_dir': local_dir,
                'oss_path': oss_path,
                'compressed_file': compressed_file,
                'compression_stats': compression_stats,
                'upload_details': upload_details,
                'checksum': checksum,
                'verified': verify_integrity
            }

            logger.log_operation("Compress and Upload", "SUCCESS", oss_path)
            return True, operation_details

        except Exception as e:
            logger.log_operation("Compress and Upload", "FAILED", str(e))
            return False, {'error': str(e)}
        finally:
            # SECURITY FIX: Ensure secure cleanup of temporary files
            try:
                if temp_fd is not None:
                    os.close(temp_fd)
            except (OSError, ValueError):
                pass  # File descriptor already closed

            try:
                if temp_file and os.path.exists(temp_file):
                    os.remove(temp_file)
                    logger.info(f"Cleaned up temporary file in finally block: {temp_file}")
            except OSError as cleanup_error:
                logger.warning(f"Failed to cleanup temporary file {temp_file}: {cleanup_error}")

    def _upload_file_to_oss_with_retry(self, local_file: str, oss_path: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Upload a file to OSS with retry logic and chunked upload support.

        Args:
            local_file: Local file path
            oss_path: OSS destination path

        Returns:
            Tuple of (success_flag, upload_details)
        """
        file_size = os.path.getsize(local_file)
        file_size_mb = file_size / (1024 * 1024)

        logger.info(f"Starting upload: {file_size_mb:.1f}MB file to {oss_path}")

        # Determine upload method based on file size
        if file_size_mb > self.chunk_size_mb:
            logger.info(f"File size ({file_size_mb:.1f}MB) exceeds chunk threshold ({self.chunk_size_mb}MB), using multipart upload")
            return self._upload_file_multipart_with_retry(local_file, oss_path)
        else:
            logger.info(f"File size ({file_size_mb:.1f}MB) within threshold, using simple upload")
            return self._upload_file_simple_with_retry(local_file, oss_path)

    def _upload_file_simple_with_retry(self, local_file: str, oss_path: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Upload a file to OSS with retry logic for simple uploads.
        """
        last_error = None

        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    logger.info(f"Upload attempt {attempt + 1}/{self.max_retries + 1} for {oss_path}")
                    time.sleep(self.retry_delay * attempt)  # Exponential backoff

                # Monitor memory before upload attempt
                memory_stats = self._monitor_memory_usage()
                if memory_stats and memory_stats['process_memory_percent'] > self.memory_threshold_percent:
                    logger.warning(f"Memory usage high before upload attempt: {memory_stats['process_memory_percent']:.1f}%")
                    self._cleanup_memory()

                # Perform upload with timeout
                success, details = self._upload_file_with_timeout(local_file, oss_path)

                if success:
                    if attempt > 0:
                        logger.info(f"Upload succeeded on attempt {attempt + 1}")
                    return True, details
                else:
                    last_error = details.get('error', 'Unknown error')
                    logger.warning(f"Upload attempt {attempt + 1} failed: {last_error}")

            except Exception as e:
                last_error = str(e)
                logger.warning(f"Upload attempt {attempt + 1} failed with exception: {last_error}")

                # Check if it's a retryable error
                if not self._is_retryable_error(e):
                    logger.error(f"Non-retryable error encountered: {last_error}")
                    break

        logger.error(f"Upload failed after {self.max_retries + 1} attempts. Last error: {last_error}")
        return False, {'error': last_error, 'attempts': self.max_retries + 1}

    def _upload_file_to_oss(self, local_file: str, oss_path: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Upload a file to OSS.

        Args:
            local_file: Local file path
            oss_path: OSS destination path

        Returns:
            Tuple of (success_flag, upload_details)
        """
        try:
            bucket = self.get_oss_bucket()
            
            # Get file size for progress tracking
            file_size = os.path.getsize(local_file)

            # Upload file
            start_time = datetime.datetime.now()
            result = bucket.put_object_from_file(oss_path, local_file)
            end_time = datetime.datetime.now()

            upload_time = (end_time - start_time).total_seconds()
            upload_speed = file_size / upload_time if upload_time > 0 else 0

            upload_details = {
                'file_size': file_size,
                'upload_time': upload_time,
                'upload_speed_mbps': upload_speed / (1024 * 1024),
                'etag': result.etag,
                'request_id': result.request_id
            }

            size_mb = file_size / (1024 * 1024)
            speed_mbps = upload_speed / (1024 * 1024)
            logger.info(f"Upload completed: {size_mb:.1f}MB in {upload_time:.1f}s ({speed_mbps:.1f}MB/s)")
            return True, upload_details

        except Exception as e:
            logger.error(f"Upload failed: {str(e)}")
            return False, {'error': str(e)}

    def _upload_file_with_timeout(self, local_file: str, oss_path: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Upload a file with timeout handling.
        """
        def upload_worker():
            try:
                bucket = self.get_oss_bucket()
                file_size = os.path.getsize(local_file)

                start_time = datetime.datetime.now()
                result = bucket.put_object_from_file(oss_path, local_file)
                end_time = datetime.datetime.now()

                upload_time = (end_time - start_time).total_seconds()
                upload_speed = file_size / upload_time if upload_time > 0 else 0

                return {
                    'success': True,
                    'file_size': file_size,
                    'upload_time': upload_time,
                    'upload_speed_mbps': upload_speed / (1024 * 1024),
                    'etag': result.etag,
                    'request_id': result.request_id
                }
            except Exception as e:
                return {'success': False, 'error': str(e)}

        try:
            # Use ThreadPoolExecutor for timeout handling
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(upload_worker)
                result = future.result(timeout=self.upload_timeout)

                if result['success']:
                    file_size_mb = result['file_size'] / (1024 * 1024)
                    speed_mbps = result['upload_speed_mbps']
                    logger.info(f"Upload completed: {file_size_mb:.1f}MB in {result['upload_time']:.1f}s ({speed_mbps:.1f}MB/s)")
                    return True, result
                else:
                    return False, result

        except FutureTimeoutError:
            error_msg = f"Upload timeout after {self.upload_timeout} seconds"
            logger.error(error_msg)
            return False, {'error': error_msg}
        except Exception as e:
            error_msg = f"Upload error: {str(e)}"
            logger.error(error_msg)
            return False, {'error': error_msg}

    def _upload_file_multipart_with_retry(self, local_file: str, oss_path: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Upload large files using multipart upload with retry logic.
        """
        logger.info(f"Starting multipart upload for {oss_path}")

        last_error = None
        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    logger.info(f"Multipart upload attempt {attempt + 1}/{self.max_retries + 1}")
                    time.sleep(self.retry_delay * attempt)

                success, details = self._upload_file_multipart(local_file, oss_path)

                if success:
                    if attempt > 0:
                        logger.info(f"Multipart upload succeeded on attempt {attempt + 1}")
                    return True, details
                else:
                    last_error = details.get('error', 'Unknown multipart error')
                    logger.warning(f"Multipart upload attempt {attempt + 1} failed: {last_error}")

            except Exception as e:
                last_error = str(e)
                logger.warning(f"Multipart upload attempt {attempt + 1} failed with exception: {last_error}")

                if not self._is_retryable_error(e):
                    break

        logger.error(f"Multipart upload failed after {self.max_retries + 1} attempts. Last error: {last_error}")
        return False, {'error': last_error, 'attempts': self.max_retries + 1}

    def _upload_file_multipart(self, local_file: str, oss_path: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Upload a file using multipart upload for better reliability with large files.
        """
        try:
            bucket = self.get_oss_bucket()
            file_size = os.path.getsize(local_file)

            # Initialize multipart upload
            upload_id = bucket.init_multipart_upload(oss_path).upload_id
            logger.info(f"Initialized multipart upload: {upload_id}")

            # Calculate part size (minimum 100KB, maximum 5GB per part)
            part_size = max(100 * 1024, min(self.chunk_size_mb * 1024 * 1024, 5 * 1024 * 1024 * 1024))
            total_parts = (file_size + part_size - 1) // part_size

            logger.info(f"Multipart upload: {total_parts} parts of {part_size / (1024 * 1024):.1f}MB each")

            parts = []
            start_time = datetime.datetime.now()

            try:
                with open(local_file, 'rb') as f:
                    for part_number in range(1, total_parts + 1):
                        # Monitor memory before each part
                        memory_stats = self._monitor_memory_usage()
                        if memory_stats and memory_stats['process_memory_percent'] > self.memory_threshold_percent:
                            logger.warning(f"Memory usage high during part {part_number}: {memory_stats['process_memory_percent']:.1f}%")
                            self._cleanup_memory()

                        # Read part data
                        part_data = f.read(part_size)
                        if not part_data:
                            break

                        # Upload part with timeout
                        logger.info(f"Uploading part {part_number}/{total_parts} ({len(part_data) / (1024 * 1024):.1f}MB)")

                        part_result = bucket.upload_part(oss_path, upload_id, part_number, part_data)
                        parts.append(oss2.models.PartInfo(part_number, part_result.etag))

                        # Log progress
                        progress_percent = (part_number / total_parts) * 100
                        logger.info(f"Multipart progress: {progress_percent:.1f}% ({part_number}/{total_parts} parts)")

                # Complete multipart upload
                logger.info("Completing multipart upload...")
                result = bucket.complete_multipart_upload(oss_path, upload_id, parts)
                end_time = datetime.datetime.now()

                upload_time = (end_time - start_time).total_seconds()
                upload_speed = file_size / upload_time if upload_time > 0 else 0

                upload_details = {
                    'file_size': file_size,
                    'upload_time': upload_time,
                    'upload_speed_mbps': upload_speed / (1024 * 1024),
                    'etag': result.etag,
                    'request_id': result.request_id,
                    'parts_count': len(parts),
                    'part_size_mb': part_size / (1024 * 1024)
                }

                file_size_mb = file_size / (1024 * 1024)
                speed_mbps = upload_speed / (1024 * 1024)
                logger.info(f"Multipart upload completed: {file_size_mb:.1f}MB in {upload_time:.1f}s ({speed_mbps:.1f}MB/s)")
                return True, upload_details

            except Exception as e:
                # Abort multipart upload on error
                logger.error(f"Multipart upload failed, aborting: {str(e)}")
                try:
                    bucket.abort_multipart_upload(oss_path, upload_id)
                    logger.info("Multipart upload aborted successfully")
                except Exception as abort_error:
                    logger.warning(f"Failed to abort multipart upload: {abort_error}")
                raise

        except Exception as e:
            logger.error(f"Multipart upload error: {str(e)}")
            return False, {'error': str(e)}

    def _is_retryable_error(self, error: Exception) -> bool:
        """
        Determine if an error is retryable.
        """
        # Network-related errors are generally retryable
        if isinstance(error, (NetworkError, RequestError, ServerError)):
            return True

        # Timeout errors are retryable
        if isinstance(error, (FutureTimeoutError, TimeoutError)):
            return True

        # Check error message for retryable conditions
        error_str = str(error).lower()
        retryable_keywords = [
            'timeout', 'connection', 'network', 'temporary', 'throttle',
            'rate limit', 'service unavailable', 'internal server error'
        ]

        return any(keyword in error_str for keyword in retryable_keywords)

    def _verify_uploaded_file(self, oss_path: str, expected_checksum: str) -> bool:
        """
        Verify the integrity of an uploaded file by comparing checksums.

        Args:
            oss_path: OSS path of the uploaded file
            expected_checksum: Expected checksum of the file

        Returns:
            True if verification passes, False otherwise
        """
        # SECURITY FIX: Use secure temporary file creation
        temp_file = None
        try:
            bucket = self.get_oss_bucket()

            # SECURITY FIX: Create secure temporary file instead of predictable naming
            # This prevents race condition attacks and unauthorized access
            with tempfile.NamedTemporaryFile(delete=False, suffix='.tmp', prefix='tngd_verify_') as temp_f:
                temp_file = temp_f.name
                logger.info(f"Created secure temporary file for verification: {temp_file}")

            # Download file to secure temporary location for verification
            bucket.get_object_to_file(oss_path, temp_file)

            # Calculate checksum of downloaded file
            actual_checksum = self.checksum_service.calculate_file_checksum(temp_file)

            # Compare checksums
            verification_passed = actual_checksum == expected_checksum

            if verification_passed:
                logger.info("Integrity verification: PASSED")
            else:
                logger.error("Integrity verification: FAILED")

            return verification_passed

        except Exception as e:
            logger.error(f"Verification error: {str(e)}")
            return False
        finally:
            # SECURITY FIX: Ensure secure cleanup of temporary file
            if temp_file and os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    logger.info(f"Cleaned up verification temporary file: {temp_file}")
                except OSError as cleanup_error:
                    logger.warning(f"Failed to cleanup verification temporary file {temp_file}: {cleanup_error}")

    def list_oss_objects(self, prefix: str = "") -> List[str]:
        """
        List objects in OSS bucket with optional prefix filter.

        Args:
            prefix: Prefix to filter objects

        Returns:
            List of object keys
        """
        try:
            bucket = self.get_oss_bucket()
            objects = []
            
            for obj in oss2.ObjectIterator(bucket, prefix=prefix):
                objects.append(obj.key)
                
            logger.info(f"Listed {len(objects)} OSS objects")
            return objects

        except Exception as e:
            logger.error(f"List objects failed: {str(e)}")
            return []

    def delete_oss_object(self, oss_path: str) -> bool:
        """
        Delete an object from OSS.

        Args:
            oss_path: OSS path of the object to delete

        Returns:
            True if deletion successful, False otherwise
        """
        try:
            bucket = self.get_oss_bucket()
            bucket.delete_object(oss_path)
            logger.info(f"Deleted: {oss_path}")
            return True

        except Exception as e:
            logger.error(f"Delete failed: {str(e)}")
            return False

    def get_object_info(self, oss_path: str) -> Optional[Dict[str, Any]]:
        """
        Get information about an OSS object.

        Args:
            oss_path: OSS path of the object

        Returns:
            Dictionary with object information or None if not found
        """
        try:
            bucket = self.get_oss_bucket()
            result = bucket.head_object(oss_path)
            
            object_info = {
                'size': result.content_length,
                'last_modified': result.last_modified,
                'etag': result.etag,
                'content_type': result.content_type
            }
            
            return object_info

        except Exception as e:
            logger.error(f"Get object info failed: {str(e)}")
            return None

    def save_locally_as_backup(self, compressed_file: str, table_name: str, date_str: str) -> Tuple[bool, str]:
        """
        Save compressed file locally as backup when OSS upload fails.

        Args:
            compressed_file: Path to compressed file
            table_name: Name of the table
            date_str: Date string for organization

        Returns:
            Tuple of (success, backup_path)
        """
        try:
            # Create local backup directory structure
            backup_base_dir = self.config.get('storage', 'local_backup_dir', 'backup_local')
            backup_dir = os.path.join(backup_base_dir, date_str.replace('-', os.sep))

            os.makedirs(backup_dir, exist_ok=True)

            # Generate backup filename
            timestamp = datetime.datetime.now().strftime('%H%M%S')
            backup_filename = f"{table_name}_{timestamp}.tar.gz"
            backup_path = os.path.join(backup_dir, backup_filename)

            # Copy compressed file to backup location
            import shutil
            shutil.copy2(compressed_file, backup_path)

            logger.info(f"Local backup saved: {backup_path}")
            return True, backup_path

        except Exception as e:
            error_msg = f"Local backup failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
