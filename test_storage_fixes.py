#!/usr/bin/env python3
"""
Test script to verify the storage manager fixes for the monthly backup process.
This script tests the enhanced StorageManager with timeout, retry, and chunked upload capabilities.
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.storage_manager import StorageManager
from core.config_manager import ConfigManager
from utils.logging_utils import logger

def create_test_data(size_mb: int = 10) -> str:
    """
    Create test data file of specified size.
    
    Args:
        size_mb: Size in megabytes
        
    Returns:
        Path to created test file
    """
    test_dir = tempfile.mkdtemp(prefix='tngd_test_')
    test_file = os.path.join(test_dir, 'test_data.txt')
    
    # Create test data
    chunk_size = 1024 * 1024  # 1MB chunks
    with open(test_file, 'w') as f:
        for i in range(size_mb):
            # Write 1MB of test data
            data = f"Test data chunk {i:04d} " * (chunk_size // 20)
            f.write(data[:chunk_size])
    
    logger.info(f"Created test data: {test_file} ({size_mb}MB)")
    return test_dir

def test_connection():
    """Test OSS connection."""
    logger.info("=== Testing OSS Connection ===")
    
    try:
        config = ConfigManager()
        storage = StorageManager(config)
        
        success, message = storage.test_connection()
        
        if success:
            logger.info("✅ OSS connection test: PASSED")
            return True
        else:
            logger.error(f"❌ OSS connection test: FAILED - {message}")
            return False
            
    except Exception as e:
        logger.error(f"❌ OSS connection test: ERROR - {str(e)}")
        return False

def test_memory_monitoring():
    """Test memory monitoring functionality."""
    logger.info("=== Testing Memory Monitoring ===")
    
    try:
        config = ConfigManager()
        storage = StorageManager(config)
        
        # Test memory monitoring
        memory_stats = storage._monitor_memory_usage()
        
        if memory_stats:
            logger.info(f"✅ Memory monitoring: {memory_stats['process_memory_mb']:.1f}MB "
                       f"({memory_stats['process_memory_percent']:.1f}%)")
            
            # Test memory cleanup
            storage._cleanup_memory()
            logger.info("✅ Memory cleanup: COMPLETED")
            return True
        else:
            logger.warning("⚠️ Memory monitoring: No stats returned")
            return False
            
    except Exception as e:
        logger.error(f"❌ Memory monitoring test: ERROR - {str(e)}")
        return False

def test_small_file_upload():
    """Test upload of small file (should use simple upload)."""
    logger.info("=== Testing Small File Upload ===")
    
    test_dir = None
    try:
        config = ConfigManager()
        storage = StorageManager(config)
        
        # Create small test data (5MB)
        test_dir = create_test_data(5)
        
        # Test upload
        oss_path = f"test/small_file_{datetime.now().strftime('%Y%m%d_%H%M%S')}.tar.gz"
        
        logger.info(f"Testing upload: {test_dir} -> {oss_path}")
        success, details = storage.compress_and_upload(test_dir, oss_path, verify_integrity=False)
        
        if success:
            logger.info("✅ Small file upload: PASSED")
            logger.info(f"Upload details: {details}")
            
            # Cleanup test file from OSS
            storage.delete_oss_object(oss_path)
            return True
        else:
            logger.error(f"❌ Small file upload: FAILED - {details}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Small file upload test: ERROR - {str(e)}")
        return False
    finally:
        if test_dir and os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def test_large_file_upload():
    """Test upload of large file (should use multipart upload)."""
    logger.info("=== Testing Large File Upload ===")
    
    test_dir = None
    try:
        config = ConfigManager()
        storage = StorageManager(config)
        
        # Create large test data (150MB to trigger multipart)
        test_dir = create_test_data(150)
        
        # Test upload
        oss_path = f"test/large_file_{datetime.now().strftime('%Y%m%d_%H%M%S')}.tar.gz"
        
        logger.info(f"Testing large file upload: {test_dir} -> {oss_path}")
        success, details = storage.compress_and_upload(test_dir, oss_path, verify_integrity=False)
        
        if success:
            logger.info("✅ Large file upload: PASSED")
            logger.info(f"Upload details: {details}")
            
            # Cleanup test file from OSS
            storage.delete_oss_object(oss_path)
            return True
        else:
            logger.error(f"❌ Large file upload: FAILED - {details}")
            
            # Check if local backup was created
            if details.get('backup_mode'):
                logger.info(f"✅ Local backup created: {details.get('local_backup')}")
                return True
            return False
            
    except Exception as e:
        logger.error(f"❌ Large file upload test: ERROR - {str(e)}")
        return False
    finally:
        if test_dir and os.path.exists(test_dir):
            shutil.rmtree(test_dir)

def main():
    """Run all storage manager tests."""
    logger.info("Starting Storage Manager Fix Verification Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Connection Test", test_connection),
        ("Memory Monitoring", test_memory_monitoring),
        ("Small File Upload", test_small_file_upload),
        ("Large File Upload", test_large_file_upload),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            logger.info("")
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {str(e)}")
            results.append((test_name, False))
            logger.info("")
    
    # Summary
    logger.info("=" * 60)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    logger.info("=" * 60)
    logger.info(f"Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All storage manager fixes are working correctly!")
        return True
    else:
        logger.warning(f"⚠️ {total - passed} test(s) failed. Check logs for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
